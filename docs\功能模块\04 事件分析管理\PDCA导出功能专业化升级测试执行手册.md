# PDCA导出功能专业化升级测试执行手册

## 📋 **测试执行概览**

**项目路径**：D:\devCode\dlcom  
**后端端口**：48080  
**前端端口**：80  
**测试重点**：专业Excel导出、三层降级机制、兼容性验证  
**执行时间**：2025年8月5日  

## 🚀 **第一步：测试环境准备指导**

### **1.1 DLCOM项目标准启动流程**

#### **后端服务启动**
```bash
# 工作目录：D:\devCode\dlcom
# 根据项目记忆，使用标准启动流程

# 情况1：代码未修改（推荐使用）
bat\start.bat

# 情况2：代码已修改
bat\compile.bat  # 先编译
bat\start.bat    # 再启动

# 验证后端服务
# 访问：http://localhost:48080/doc.html
# 确认Swagger文档正常显示
```

#### **前端服务启动**
```bash
# 工作目录：D:\devCode\dlcom\nms-ui
npm run dev

# 验证前端服务
# 访问：http://localhost:80
# 确认登录页面正常显示
```

### **1.2 测试环境配置确认**

#### **必要环境检查清单**
- [ ] **MySQL数据库**：确认数据库服务运行，连接正常
- [ ] **Java运行环境**：JDK 8+，支持Spring Boot应用
- [ ] **Node.js环境**：Node.js 16+，支持Vue.js项目
- [ ] **pnpm包管理器**：如未安装，使用npm作为替代
- [ ] **浏览器环境**：Chrome或Edge，开启开发者工具

#### **服务状态验证**
```bash
# 检查端口占用
netstat -ano | findstr :48080  # 后端端口
netstat -ano | findstr :80     # 前端端口

# 检查服务响应
curl http://localhost:48080/actuator/health  # 后端健康检查
curl http://localhost:80                     # 前端页面检查
```

### **1.3 关键依赖验证**

#### **后端依赖检查**
- [ ] **EasyExcel框架**：确认nms-spring-boot-starter-excel依赖正常
- [ ] **PDCAExcelStyleHandler**：验证样式处理器类存在
- [ ] **专业Excel API**：确认GET /pdca/{id}/export/excel接口可访问

#### **前端依赖检查**
- [ ] **MultiFormatExporter工具**：确认导出工具类正常
- [ ] **Element Plus组件**：确认UI组件库加载正常
- [ ] **Vue.js框架**：确认前端框架运行正常

### **1.4 浏览器工具状态检查**

#### **Playwright工具问题处理**
根据项目记忆，如遇"Not connected"错误：

```bash
# 标准处理流程
# 1. 清理Chrome进程
taskkill /F /IM chrome.exe

# 2. 重装Playwright浏览器
npx playwright install chromium

# 3. 验证前后端服务状态
# 确保服务正常运行

# 4. 测试浏览器连接
# 使用开发者工具验证页面加载
```

## 🎯 **第二步：专业Excel导出功能核心测试**

### **2.1 测试路径导航**

#### **正确测试路径**（重要！）
```
1. 访问 http://localhost:80
2. 登录系统（使用测试账号）
3. 进入"事件分析管理"模块
4. 选择任一事件记录
5. 点击"详情"按钮进入事件分析详情页面
6. 点击"分析工具"标签页
7. 选择"PDCA分析工具"
8. 填写测试数据
9. 点击"导出"按钮，选择"Excel格式"
```

#### **错误路径避免**
❌ **禁止使用**：事件分析列表页面的"分析工具"按钮（已删除）  
✅ **正确使用**：事件分析详情页面的"分析工具"标签页

### **2.2 优先级策略验证**

#### **API调用监控**
打开浏览器开发者工具，监控Network面板：

```javascript
// 预期API调用顺序
1. POST /pdca/save                    // 保存PDCA数据
2. GET /pdca/{eventId}/{analysisId}   // 获取PDCA数据ID
3. GET /pdca/{id}/export/excel        // 专业Excel导出（优先）
```

#### **控制台日志验证**
在Console面板中查找关键日志：

```javascript
// 成功调用专业Excel API的日志
"🎯 尝试使用专业Excel API导出..."
"✅ 专业Excel API导出成功"

// 降级机制触发的日志
"⚠️ 专业Excel API导出失败，降级到前端Excel:"
```

### **2.3 专业样式应用检查**

#### **Excel文件样式验证清单**

**标题样式验证**：
- [ ] 字体：18pt微软雅黑
- [ ] 背景色：医疗蓝（#2E86AB）
- [ ] 字体颜色：白色
- [ ] 对齐方式：居中
- [ ] 边框：中等边框

**表头样式验证**：
- [ ] 字体：12pt微软雅黑加粗
- [ ] 背景色：浅蓝（#E8F4FD）
- [ ] 对齐方式：居中
- [ ] 边框：细边框

**PDCA阶段标题验证**：
- [ ] 字体：14pt微软雅黑加粗
- [ ] 背景色：深粉红（#A23B72）
- [ ] 字体颜色：白色
- [ ] 对齐方式：居中

**数据行样式验证**：
- [ ] 字体：11pt微软雅黑
- [ ] 交替行背景：浅灰色（#F8F9FA）
- [ ] 边框：统一细边框（#D1D5DB）
- [ ] 自动换行：启用

### **2.4 五大报告区域完整性验证**

#### **区域1：报告信息区域**
验证Excel文件包含以下信息：
- [ ] 事件编号（eventNumber）
- [ ] 事件类型（eventType）
- [ ] 事件名称（eventName）
- [ ] 发生时间（occurrenceTime）
- [ ] 分析日期（analysisDate）
- [ ] 最后更新时间（lastUpdateTime）

#### **区域2：PDCA循环分析概览**
验证四个阶段的结构化展示：

**Plan阶段概览**：
- [ ] 状态（status）
- [ ] 负责人（responsible）
- [ ] 目标（goal）
- [ ] 方法（method）

**Do阶段概览**：
- [ ] 状态（status）
- [ ] 负责人（responsible）
- [ ] 进度（progress）
- [ ] 实施情况（implementation）

**Check阶段概览**：
- [ ] 状态（status）
- [ ] 负责人（responsible）
- [ ] 结果（results）
- [ ] 评估（evaluation）

**Act阶段概览**：
- [ ] 状态（status）
- [ ] 负责人（responsible）
- [ ] 标准化（standardization）
- [ ] 改进措施（improvements）

#### **区域3：分析总结与改进建议**
验证包含以下内容：
- [ ] 主要问题（mainProblem）
- [ ] 根本原因（rootCause）
- [ ] 改进措施（improvements）
- [ ] 预期效果（expectedResults）
- [ ] 下步计划（nextSteps）
- [ ] 关键指标（keyMetrics）
- [ ] 分析结论（conclusion）

#### **区域4：报告元数据**
验证元数据信息：
- [ ] 报告版本（version）
- [ ] 分析人员（analyst）
- [ ] 导出时间（exportTime）
- [ ] 报告状态（status）
- [ ] 分析类型（analysisType）
- [ ] 部门信息（department）

#### **区域5：详细分析内容**
验证完整PDCA数据结构保留：
- [ ] 原有pdcaData结构完整
- [ ] 主要问题和结论保留
- [ ] 向后兼容性保证

## 🔄 **第三步：三层降级机制测试验证**

### **3.1 第一层测试：正常专业Excel API**

#### **测试步骤**：
1. 确保前后端服务正常运行
2. 按照标准路径进行Excel导出
3. 验证专业Excel API成功调用

#### **成功标志**：
- [ ] Network面板显示GET /pdca/{id}/export/excel请求成功（200状态码）
- [ ] Console显示"✅ 专业Excel API导出成功"
- [ ] 下载的Excel文件应用了专业医疗样式
- [ ] 文件名格式：PDCA分析报告_{id}_{timestamp}.xlsx

### **3.2 第二层测试：API失败降级到前端Excel**

#### **模拟API失败方法**：

**方法1：临时停止后端服务**
```bash
# 停止后端服务模拟API不可用
# 在bat\start.bat窗口按Ctrl+C停止服务
```

**方法2：网络断开模拟**
```javascript
// 在浏览器开发者工具中模拟离线状态
// Network面板 → 选择"Offline"
```

#### **降级验证**：
- [ ] Console显示"⚠️ 专业Excel API导出失败，降级到前端Excel:"
- [ ] 自动调用MultiFormatExporter.export()
- [ ] 成功下载前端生成的Excel文件
- [ ] 用户收到适当的提示信息

### **3.3 第三层测试：JSON格式最终降级**

#### **模拟前端Excel失败**：
需要在代码中临时模拟MultiFormatExporter失败，或通过浏览器限制模拟。

#### **最终降级验证**：
- [ ] 下载JSON格式文件
- [ ] 文件名格式：PDCA分析报告_{eventType}_{eventId}_{timestamp}_V1.0.json
- [ ] 显示"Excel导出失败，已导出JSON格式"警告
- [ ] JSON文件包含完整PDCA数据

### **3.4 用户体验验证**

#### **Loading状态管理**：
- [ ] 导出开始时显示loading提示
- [ ] loading消息格式："正在导出Excel格式文件..."
- [ ] 成功/失败后loading正确关闭
- [ ] 异常情况下loading也能正确关闭

#### **消息提示友好性**：
- [ ] 成功提示："专业Excel报告导出成功！"
- [ ] 降级提示：包含降级原因说明
- [ ] 错误提示：清晰描述问题和建议
- [ ] 提示时机：在适当的时候显示，不干扰用户操作

#### **降级透明性**：
- [ ] 用户无需了解技术细节
- [ ] 降级过程不中断用户操作
- [ ] 最终都能获得可用的导出文件
- [ ] 体验连续性良好

## 🔧 **第四步：全面兼容性和稳定性测试**

### **4.1 多格式导出测试**

#### **Word导出测试**：
1. 在PDCA分析工具中点击"导出" → "Word格式"
2. 验证调用MultiFormatExporter.export(data, ExportFormat.WORD)
3. 确认下载.docx格式文件
4. 检查Word文件内容完整性

#### **PDF导出测试**：
1. 在PDCA分析工具中点击"导出" → "PDF格式"
2. 验证调用MultiFormatExporter.export(data, ExportFormat.PDF)
3. 确认下载.pdf格式文件
4. 检查PDF文件布局和可读性

#### **多格式兼容性验证**：
- [ ] Excel、Word、PDF三种格式都能正常导出
- [ ] 各格式文件内容完整且格式正确
- [ ] 导出过程互不干扰
- [ ] 用户可以连续导出不同格式

### **4.2 原有API兼容性测试**

#### **原有导出API测试**：
```bash
# 直接测试原有API接口
curl -X GET "http://localhost:48080/pdca/{id}/export" \
     -H "accept: application/json"
```

#### **API兼容性验证**：
- [ ] GET /pdca/{id}/export接口正常返回JSON数据
- [ ] 返回数据结构与升级前一致
- [ ] 接口响应时间正常
- [ ] 权限控制机制不变

### **4.3 PDCA分析流程完整性测试**

#### **完整流程测试**：
1. **数据输入测试**：
   - [ ] Plan阶段数据输入和保存
   - [ ] Do阶段数据输入和保存
   - [ ] Check阶段数据输入和保存
   - [ ] Act阶段数据输入和保存

2. **数据保存测试**：
   - [ ] 自动保存功能正常
   - [ ] 手动保存功能正常
   - [ ] 数据持久化正确
   - [ ] 保存状态提示清晰

3. **阶段切换测试**：
   - [ ] 四个阶段间切换流畅
   - [ ] 数据在切换时保持
   - [ ] 进度显示正确
   - [ ] 完成状态标记准确

4. **导出触发测试**：
   - [ ] 导出按钮响应正常
   - [ ] 格式选择功能正确
   - [ ] 导出前数据验证
   - [ ] 导出过程状态反馈

### **4.4 权限控制测试**

#### **用户角色测试**：
如果系统有不同用户角色，测试：
- [ ] 管理员用户：所有导出功能可用
- [ ] 普通用户：按权限限制功能
- [ ] 只读用户：导出功能受限或禁用
- [ ] 权限不足时的友好提示

#### **权限验证场景**：
- [ ] 登录用户的导出权限
- [ ] 未登录用户的访问限制
- [ ] 权限变更后的功能更新
- [ ] 权限异常的错误处理

### **4.5 并发和稳定性测试**

#### **并发导出测试**：
1. 多个浏览器标签页同时导出
2. 不同用户同时导出相同数据
3. 短时间内连续多次导出
4. 大数据量导出的性能测试

#### **稳定性验证**：
- [ ] 系统在并发情况下保持稳定
- [ ] 内存使用在合理范围内
- [ ] 文件生成不出现冲突
- [ ] 错误恢复机制有效

## 📊 **第五步：测试结果分析和问题诊断**

### **5.1 功能性验证标准**

#### **专业Excel样式符合医疗行业标准**：
- [ ] 配色方案专业且符合医疗行业视觉规范
- [ ] 布局清晰，信息层次分明
- [ ] 字体和字号符合可读性要求
- [ ] 整体风格专业、正式

#### **数据完整性和准确性**：
- [ ] 所有输入数据正确导出
- [ ] 数据格式转换准确
- [ ] 特殊字符和中文显示正常
- [ ] 数值计算和统计正确

### **5.2 性能测试标准**

#### **导出速度测试**：
- [ ] 小数据量（<1MB）：3秒内完成
- [ ] 中等数据量（1-5MB）：10秒内完成
- [ ] 大数据量（>5MB）：30秒内完成或提供进度提示

#### **文件大小合理性**：
- [ ] Excel文件大小在预期范围内
- [ ] 样式应用不显著增加文件大小
- [ ] 压缩效果良好

### **5.3 错误处理验证**

#### **异常情况处理**：
- [ ] 网络异常时的降级处理
- [ ] 服务异常时的错误提示
- [ ] 数据异常时的验证机制
- [ ] 权限异常时的友好提示

#### **用户体验保证**：
- [ ] 错误信息清晰易懂
- [ ] 提供解决问题的建议
- [ ] 不中断用户的正常操作
- [ ] 保持界面响应性

### **5.4 向后兼容性确认**

#### **现有功能完全不受影响**：
- [ ] 原有PDCA分析功能正常
- [ ] 原有导出API功能正常
- [ ] 原有用户界面不变
- [ ] 原有数据结构兼容

#### **升级透明性**：
- [ ] 用户无需学习新操作
- [ ] 现有工作流程不变
- [ ] 数据迁移无需用户干预
- [ ] 功能增强对用户透明

### **5.5 渐进式升级效果验证**

#### **新功能作为增强选项**：
- [ ] 专业Excel作为优选项提供
- [ ] 原有功能作为备选保留
- [ ] 用户可以选择使用方式
- [ ] 升级过程平滑无感知

#### **可选性验证**：
- [ ] 新功能可以独立启用/禁用
- [ ] 不依赖新功能也能正常工作
- [ ] 配置灵活，适应不同需求
- [ ] 未来扩展预留空间

## 🎯 **测试执行检查清单**

### **环境准备检查清单**
- [ ] 后端服务启动成功（端口48080）
- [ ] 前端服务启动成功（端口80）
- [ ] MySQL数据库连接正常
- [ ] 浏览器开发者工具已开启
- [ ] 测试账号登录成功

### **专业Excel导出检查清单**
- [ ] 从正确路径进入PDCA分析工具
- [ ] 专业Excel API优先调用成功
- [ ] Excel文件应用医疗蓝配色方案
- [ ] 五大报告区域内容完整
- [ ] 样式符合医疗行业标准

### **降级机制检查清单**
- [ ] 正常情况专业Excel API成功
- [ ] API失败时降级到前端Excel
- [ ] 极端情况降级到JSON格式
- [ ] 用户体验保持连续性
- [ ] 错误提示友好清晰

### **兼容性测试检查清单**
- [ ] Word导出功能正常
- [ ] PDF导出功能正常
- [ ] 原有API功能不受影响
- [ ] PDCA分析流程完整
- [ ] 权限控制机制正常

### **质量验证检查清单**
- [ ] 导出速度在合理范围
- [ ] 文件大小适中
- [ ] 错误处理机制完善
- [ ] 向后兼容性保证
- [ ] 渐进式升级成功

## 📞 **技术支持和问题反馈**

### **问题反馈信息收集**
如遇到问题，请提供以下信息：
1. **具体操作步骤**：详细描述操作过程
2. **错误现象**：截图或错误信息
3. **浏览器控制台日志**：Console和Network面板信息
4. **后端服务日志**：相关错误日志（如可获取）
5. **环境信息**：操作系统、浏览器版本等

### **常见问题快速解决**

**问题1：专业Excel API未调用**
```javascript
// 检查控制台是否有以下日志
console.log('🎯 尝试使用专业Excel API导出...')
// 如果没有，检查pdcaResult.id是否存在
```

**问题2：样式不正确**
- 检查Excel文件标题是否为医疗蓝背景
- 验证表头是否为浅蓝背景
- 确认PDCA阶段标题是否为深粉红背景

**问题3：降级机制未触发**
```javascript
// 应该看到以下日志
console.warn('⚠️ 专业Excel API导出失败，降级到前端Excel:', apiError)
```

**问题4：文件下载失败**
- 检查浏览器下载设置
- 验证文件权限
- 确认磁盘空间充足

### **关键文件位置参考**
- **前端导出逻辑**：`nms-ui/src/views/evt/analyze/components/AnalysisWorkspace.vue`
- **后端API接口**：`nms-module-evt/.../PDCAAnalysisController.java`
- **样式处理器**：`nms-module-evt/.../PDCAExcelStyleHandler.java`
- **数据服务**：`nms-module-evt/.../PDCAAnalysisServiceImpl.java`

---

**测试执行手册版本**：V1.0.0
**创建时间**：2025年8月5日
**适用项目**：DLCOM医疗事件分析管理系统
**技术栈**：Spring Boot + Vue.js + EasyExcel + Element Plus
**测试重点**：PDCA导出功能专业化升级全面验证
**技术支持**：基于DLCOM项目记忆和代码分析的专业指导
