package com.elkj.nms.module.evt.util;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.List;

/**
 * PDCA分析报告专业Excel样式处理器
 * 
 * 实现医疗质量管理报告的专业样式设计：
 * - 医疗行业标准配色方案
 * - 专业表格样式和布局
 * - 清晰的数据层次结构
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025-08-04
 */
public class PDCAExcelStyleHandler implements CellWriteHandler {

    /**
     * 医疗行业专业配色方案
     */
    public static class MedicalColors {
        // 主色调：医疗蓝
        public static final byte[] PRIMARY_BLUE = {46, -122, -85}; // #2E86AB
        
        // 辅助色：深粉红  
        public static final byte[] SECONDARY_PINK = {-94, 59, 114}; // #A23B72
        
        // 强调色：橙色
        public static final byte[] ACCENT_ORANGE = {-15, -113, 1}; // #F18F01
        
        // 背景色：浅蓝
        public static final byte[] LIGHT_BLUE_BG = {-24, -12, -3}; // #E8F4FD
        
        // 交替行背景：浅灰
        public static final byte[] ALTERNATE_ROW_BG = {-8, -7, -6}; // #F8F9FA
        
        // 边框色：中灰
        public static final byte[] BORDER_GRAY = {-47, -43, -37}; // #D1D5DB
        
        // 文字色：深灰
        public static final byte[] TEXT_DARK = {55, 65, 81}; // #374151
    }

    private Workbook workbook;
    private CellStyle titleStyle;
    private CellStyle headerStyle;
    private CellStyle dataStyle;
    private CellStyle alternateRowStyle;
    private CellStyle phaseHeaderStyle;

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, 
                                Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        // 初始化样式
        if (workbook == null) {
            workbook = writeSheetHolder.getSheet().getWorkbook();
            initializeStyles();
        }
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, 
                               Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 应用样式
        applyCellStyle(cell, relativeRowIndex);
    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, 
                                      WriteCellData<?> cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 数据转换后的处理
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, 
                                 List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 单元格处理完成后的清理工作
    }

    /**
     * 初始化所有样式
     */
    private void initializeStyles() {
        // 创建标题样式
        titleStyle = createTitleStyle();
        
        // 创建表头样式
        headerStyle = createHeaderStyle();
        
        // 创建数据样式
        dataStyle = createDataStyle();
        
        // 创建交替行样式
        alternateRowStyle = createAlternateRowStyle();
        
        // 创建阶段标题样式
        phaseHeaderStyle = createPhaseHeaderStyle();
    }

    /**
     * 创建标题样式 - 18pt微软雅黑，医疗蓝背景，白色字体
     */
    private CellStyle createTitleStyle() {
        CellStyle style = workbook.createCellStyle();

        // 字体设置
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 18);
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);

        // 背景色：医疗蓝 #2E86AB
        if (workbook instanceof XSSFWorkbook) {
            XSSFColor medicalBlue = new XSSFColor(MedicalColors.PRIMARY_BLUE, null);
            ((org.apache.poi.xssf.usermodel.XSSFCellStyle) style).setFillForegroundColor(medicalBlue);
        } else {
            // 降级为标准蓝色
            style.setFillForegroundColor(IndexedColors.BLUE.getIndex());
        }
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 边框
        setBorders(style, BorderStyle.MEDIUM);

        return style;
    }

    /**
     * 创建表头样式 - 12pt加粗，浅蓝背景，居中对齐
     */
    private CellStyle createHeaderStyle() {
        CellStyle style = workbook.createCellStyle();

        // 字体设置
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 12);
        font.setBold(true);
        style.setFont(font);

        // 背景色：浅蓝 #E8F4FD
        if (workbook instanceof XSSFWorkbook) {
            XSSFColor lightBlue = new XSSFColor(MedicalColors.LIGHT_BLUE_BG, null);
            ((org.apache.poi.xssf.usermodel.XSSFCellStyle) style).setFillForegroundColor(lightBlue);
        } else {
            // 降级为标准浅蓝色
            style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        }
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 边框
        setBorders(style, BorderStyle.THIN);

        return style;
    }

    /**
     * 创建数据样式 - 11pt常规，左对齐，自动换行
     */
    private CellStyle createDataStyle() {
        CellStyle style = workbook.createCellStyle();

        // 字体设置
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 11);
        // 设置深灰色文字
        if (workbook instanceof XSSFWorkbook) {
            XSSFColor textDark = new XSSFColor(MedicalColors.TEXT_DARK, null);
            ((org.apache.poi.xssf.usermodel.XSSFFont) font).setColor(textDark);
        }
        style.setFont(font);

        // 对齐方式
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 边框
        setBorders(style, BorderStyle.THIN);

        // 自动换行
        style.setWrapText(true);

        return style;
    }

    /**
     * 创建交替行样式 - 浅灰背景提升可读性
     */
    private CellStyle createAlternateRowStyle() {
        CellStyle style = workbook.createCellStyle();

        // 字体设置（与数据样式一致）
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 11);
        if (workbook instanceof XSSFWorkbook) {
            XSSFColor textDark = new XSSFColor(MedicalColors.TEXT_DARK, null);
            ((org.apache.poi.xssf.usermodel.XSSFFont) font).setColor(textDark);
        }
        style.setFont(font);

        // 背景色：浅灰 #F8F9FA
        if (workbook instanceof XSSFWorkbook) {
            XSSFColor alternateRowBg = new XSSFColor(MedicalColors.ALTERNATE_ROW_BG, null);
            ((org.apache.poi.xssf.usermodel.XSSFCellStyle) style).setFillForegroundColor(alternateRowBg);
        } else {
            // 降级为标准浅灰色
            style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        }
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 对齐方式
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 边框
        setBorders(style, BorderStyle.THIN);

        // 自动换行
        style.setWrapText(true);

        return style;
    }

    /**
     * 创建PDCA阶段标题样式 - 14pt加粗，深粉红背景
     */
    private CellStyle createPhaseHeaderStyle() {
        CellStyle style = workbook.createCellStyle();

        // 字体设置
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 14);
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);

        // 背景色：深粉红 #A23B72
        if (workbook instanceof XSSFWorkbook) {
            XSSFColor secondaryPink = new XSSFColor(MedicalColors.SECONDARY_PINK, null);
            ((org.apache.poi.xssf.usermodel.XSSFCellStyle) style).setFillForegroundColor(secondaryPink);
        } else {
            // 降级为标准玫瑰色
            style.setFillForegroundColor(IndexedColors.ROSE.getIndex());
        }
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 边框
        setBorders(style, BorderStyle.MEDIUM);

        return style;
    }

    /**
     * 设置边框 - 统一细边框，专业整洁
     */
    private void setBorders(CellStyle style, BorderStyle borderStyle) {
        style.setBorderTop(borderStyle);
        style.setBorderBottom(borderStyle);
        style.setBorderLeft(borderStyle);
        style.setBorderRight(borderStyle);

        // 设置边框颜色为中灰色 #D1D5DB
        if (workbook instanceof XSSFWorkbook) {
            XSSFColor borderGray = new XSSFColor(MedicalColors.BORDER_GRAY, null);
            org.apache.poi.xssf.usermodel.XSSFCellStyle xssfStyle =
                (org.apache.poi.xssf.usermodel.XSSFCellStyle) style;
            xssfStyle.setTopBorderColor(borderGray);
            xssfStyle.setBottomBorderColor(borderGray);
            xssfStyle.setLeftBorderColor(borderGray);
            xssfStyle.setRightBorderColor(borderGray);
        }
    }

    /**
     * 应用单元格样式 - 智能识别内容类型并应用相应样式
     */
    private void applyCellStyle(Cell cell, Integer relativeRowIndex) {
        try {
            String cellValue = "";
            if (cell.getCellType() == CellType.STRING) {
                cellValue = cell.getStringCellValue();
            } else if (cell.getCellType() == CellType.NUMERIC) {
                cellValue = String.valueOf(cell.getNumericCellValue());
            }

            // 根据内容和行号应用不同样式
            if (cellValue.contains("医疗质量管理") && cellValue.contains("PDCA分析报告")) {
                // 主标题行 - 18pt微软雅黑，医疗蓝背景，白色字体
                cell.setCellStyle(titleStyle);
            } else if (cellValue.contains("Plan阶段") || cellValue.contains("Do阶段") ||
                       cellValue.contains("Check阶段") || cellValue.contains("Act阶段") ||
                       cellValue.contains("计划阶段") || cellValue.contains("执行阶段") ||
                       cellValue.contains("检查阶段") || cellValue.contains("行动阶段")) {
                // PDCA阶段标题 - 14pt加粗，深粉红背景
                cell.setCellStyle(phaseHeaderStyle);
            } else if (cellValue.contains("项目") || cellValue.contains("内容") ||
                       cellValue.contains("状态") || cellValue.contains("负责人") ||
                       cellValue.contains("截止时间") || cellValue.contains("事件编号") ||
                       cellValue.contains("事件类型")) {
                // 表头 - 12pt加粗，浅蓝背景，居中对齐
                cell.setCellStyle(headerStyle);
            } else if (relativeRowIndex != null && relativeRowIndex > 0 && relativeRowIndex % 2 == 0) {
                // 偶数行 - 交替行样式，浅灰背景提升可读性
                cell.setCellStyle(alternateRowStyle);
            } else {
                // 奇数行 - 普通数据样式，11pt常规，左对齐，自动换行
                cell.setCellStyle(dataStyle);
            }
        } catch (Exception e) {
            // 异常情况下使用默认数据样式
            cell.setCellStyle(dataStyle);
        }
    }
}
