# 寸止(Cunzhi)MCP配置与使用指南

## 📋 概述

寸止(<PERSON>unzhi)MCP是AURA-X协议中的强制交互网关，确保AI绝不自作主张，所有决策权完全掌握在用户手中。

## 🔧 寸止MCP的核心功能

### 1. 智能代码审查交互工具
```javascript
{
  "name": "zhi___",
  "description": "智能代码审查交互工具，支持预定义选项、自由文本输入和图片上传",
  "parameters": {
    "message": "要显示给用户的消息",
    "predefined_options": "预定义的选项列表（可选）",
    "is_markdown": "消息是否为Markdown格式，默认为true"
  }
}
```

### 2. 使用场景

#### 需求澄清
```javascript
// 当需求不明确时
zhi___({
  message: "请澄清您的具体需求：",
  predefined_options: [
    "选项1：具体功能描述",
    "选项2：另一种理解",
    "选项3：第三种可能",
    "自定义需求：我来详细描述"
  ]
})
```

#### 方案选择
```javascript
// 当存在多个技术方案时
zhi___({
  message: "发现多个可行的技术方案，请选择：",
  predefined_options: [
    "方案A：Vue组件重构方案",
    "方案B：React迁移方案", 
    "方案C：原生JS实现方案",
    "需要更多信息：请详细对比各方案"
  ]
})
```

#### 任务确认
```javascript
// 任务完成前的最终确认
zhi___({
  message: "任务即将完成，请确认：",
  predefined_options: [
    "确认完成：按计划执行",
    "需要调整：我有新的要求",
    "暂停执行：稍后继续",
    "重新规划：重新分析需求"
  ]
})
```

## 🎯 AURA-X协议中的寸止规则

### 核心原则

#### 1. 绝对控制 (Absolute Control)
- AI的任何行动、提议或询问都必须通过寸止MCP进行
- 禁止任何形式的直接询问或推测性操作
- 用户拥有最终决策权

#### 2. 唯一询问渠道
- **只能**通过寸止MCP对用户进行询问
- 严禁使用任何其他方式直接向用户提问，包括在任务结束时

#### 3. 强制交互节点
- **需求不明确时**：必须使用寸止提供预定义选项，让用户澄清需求
- **存在多个方案时**：必须使用寸止将所有可行方案作为选项列出，供用户选择
- **计划或策略变更时**：必须通过寸止提出并获得用户批准
- **任务完成前**：必须调用寸止请求最终反馈和完成确认

#### 4. 禁止主动结束
- 在没有通过寸止获得用户明确的"可以完成/结束任务"指令前，严禁AI单方面结束对话或任务

## 🛠️ 如何在项目中配置寸止MCP

### 1. MCP工具注册

在MCP配置文件中注册寸止工具：

```json
{
  "tools": [
    {
      "name": "zhi___",
      "description": "智能代码审查交互工具，支持预定义选项、自由文本输入和图片上传",
      "parameters": {
        "properties": {
          "message": {
            "description": "要显示给用户的消息",
            "type": "string"
          },
          "predefined_options": {
            "description": "预定义的选项列表（可选）",
            "items": {"type": "string"},
            "type": "array"
          },
          "is_markdown": {
            "description": "消息是否为Markdown格式，默认为true",
            "type": "boolean"
          }
        },
        "required": ["message"],
        "type": "object"
      }
    }
  ]
}
```

### 2. 协议配置

在AI助手的系统提示中配置AURA-X协议规则：

```markdown
## 寸止 (Cunzhi) 强制交互规则

- **唯一询问渠道**：只能通过寸止MCP对用户进行询问
- **需求不明确时**：必须使用寸止提供预定义选项
- **存在多个方案时**：必须使用寸止将所有可行方案列出
- **计划变更时**：必须通过寸止获得用户批准
- **任务完成前**：必须调用寸止请求最终确认
- **禁止主动结束**：严禁AI单方面结束对话
```

### 3. 实际使用示例

#### 代码重构场景
```javascript
// 发现需要重构的复杂组件
zhi___({
  message: `# 发现代码重构需求

检测到InfoModal.vue组件过于复杂(3246行)，建议进行重构。

## 可选方案：`,
  predefined_options: [
    "方案1：按功能模块拆分为多个子组件",
    "方案2：使用Composition API重构",
    "方案3：迁移到新的组件架构",
    "暂不处理：继续其他任务",
    "需要更多信息：详细分析重构影响"
  ]
})
```

#### 技术选型场景
```javascript
// 技术栈升级决策
zhi___({
  message: `# 技术栈升级建议

当前项目使用Spring Boot 2.7.18，建议考虑升级：

## 升级选项：`,
  predefined_options: [
    "立即升级到Spring Boot 3.x",
    "制定渐进式升级计划",
    "保持当前版本，专注业务功能",
    "需要评估：分析升级风险和收益"
  ]
})
```

## 📚 最佳实践

### 1. 消息设计原则
- **清晰明确**：消息内容要简洁明了
- **结构化**：使用Markdown格式提高可读性
- **选项完整**：预定义选项要覆盖主要场景
- **留有余地**：总是提供"自定义"或"需要更多信息"选项

### 2. 选项设计技巧
- **互斥性**：选项之间应该是互斥的
- **完整性**：覆盖用户可能的所有需求
- **层次性**：按重要性或逻辑顺序排列
- **灵活性**：提供开放式选项

### 3. 交互时机
- **决策点**：任何需要用户决策的时刻
- **歧义点**：需求或方案不明确时
- **风险点**：可能影响项目的重要操作前
- **完成点**：任务完成前的最终确认

## 🔍 调试和监控

### 1. 交互日志
记录所有寸止交互的日志，包括：
- 交互时间
- 消息内容
- 用户选择
- 后续行动

### 2. 效果评估
定期评估寸止协议的效果：
- 用户满意度
- 决策准确性
- 交互效率
- 错误率降低

## 🚀 高级配置

### 1. 动态选项生成
根据上下文动态生成选项：

```javascript
const generateOptions = (context) => {
  const baseOptions = ["继续执行", "暂停任务", "重新规划"];
  
  if (context.hasRisks) {
    baseOptions.push("风险评估：详细分析潜在风险");
  }
  
  if (context.hasAlternatives) {
    baseOptions.push("查看替代方案");
  }
  
  return baseOptions;
};
```

### 2. 上下文感知
根据项目状态和用户历史选择优化交互：

```javascript
const contextAwareInteraction = (projectState, userHistory) => {
  const message = generateContextualMessage(projectState);
  const options = generatePersonalizedOptions(userHistory);
  
  return zhi___({
    message,
    predefined_options: options
  });
};
```

---

## 📋 总结

寸止MCP是确保AI与用户协作质量的关键工具。通过严格遵循寸止协议，可以：

- **提高决策质量**：用户始终掌握主导权
- **减少误解**：强制澄清需求和方案
- **增强信任**：透明的交互过程
- **提升效率**：结构化的选择流程

正确配置和使用寸止MCP，将显著提升AI助手的协作体验和工作效果。

---

*文档版本: v1.0*  
*更新时间: 2025-08-05*  
*适用协议: AURA-X (Cunzhi Edition)*
